"use client";
import React from "react";
import { ProductLayout } from "./components/ProductLayout";
import { ProductInfo } from "./components/ProductInfo";
import { ProductContent } from "./components/ProductContent";
import { ProductDetailsPageProps } from "./types";
import { ProductProvider } from "./context/ProductContext";
import { StickyAddToCart } from "./StickyAddToCart";
import { useWhatsInsideVisibility } from "./hooks";

/**
 * ProductDetailsPage Component
 *
 * Main product details page component with server-side data:
 * - Receives all data as props from server-side fetching
 * - Eliminates prop drilling by passing data to child components
 * - Provides clean component structure
 * - Maintains identical UI and functionality
 * - Loads instantly without loading states
 */
export const ProductDetailsPage: React.FC<ProductDetailsPageProps> = ({
  strapiProduct,
  medusaProduct,
  onAddToCart,
  onCouponClick,
  productType = "VARIANT",
  bundleVariants = [],
}) => {
  console.log("🔍 ProductDetailsPage Component Debug:", {
    productType,
    bundleVariantsLength: bundleVariants?.length || 0,
    strapiProductTitle: strapiProduct?.title,
    isBundleProduct: productType === "BYOB" || productType === "COMBO",
  });

  // Hook for tracking What's Inside section visibility relative to announcement bar
  const { shouldHideSticky, setWhatsInsideElement } =
    useWhatsInsideVisibility();

  // Apply theme styles using CSS custom properties
  const themeStyles = {
    "--product-primary-color": strapiProduct?.primary_color || "#036A38",
    "--product-background-color": strapiProduct?.bg_color || "#ffffff",
  } as React.CSSProperties;

  return (
    <div style={themeStyles}>
      <ProductProvider
        strapiProduct={strapiProduct || null}
        medusaProduct={medusaProduct || null}
      >
        <ProductLayout>
          {/* Product Information Section */}
          <ProductInfo
            strapiProduct={strapiProduct}
            medusaProduct={medusaProduct}
            onAddToCart={onAddToCart}
            onCouponClick={onCouponClick}
            productType={productType}
            bundleVariants={bundleVariants}
          />

          {/* Product Content Section */}
          <ProductContent
            strapiProduct={strapiProduct}
            medusaProduct={medusaProduct}
            productType={productType}
            bundleVariants={bundleVariants}
            onWhatsInsideMount={setWhatsInsideElement}
          />
        </ProductLayout>

        {/* Sticky Add to Cart Component */}
        <StickyAddToCart
          strapiProduct={strapiProduct}
          medusaProduct={medusaProduct}
          onAddToCart={onAddToCart}
          onCouponClick={onCouponClick}
          productType={productType}
          bundleVariants={bundleVariants}
          isVisible={!shouldHideSticky}
        />
      </ProductProvider>
    </div>
  );
};

// Export as default for backward compatibility
export default ProductDetailsPage;
